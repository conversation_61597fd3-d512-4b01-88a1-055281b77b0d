# 智语慧聊数据库设计

## 数据库表结构设计

### 1. 用户表 (users)
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    nickname VA<PERSON>HA<PERSON>(50),
    avatar VARCHAR(255),
    theme_preference VARCHAR(20) DEFAULT 'auto',  -- 'light', 'dark', 'auto'
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 2. 会话表 (conversations)
```sql
CREATE TABLE conversations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    title VARCHAR(255) DEFAULT '新对话',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIG<PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### 3. 消息表 (messages)
```sql
CREATE TABLE messages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    conversation_id INTEGER NOT NULL,
    role VARCHAR(10) NOT NULL,  -- 'user' 或 'assistant'
    content TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE
);
```

### 4. 用户设置表 (user_settings)
```sql
CREATE TABLE user_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER UNIQUE NOT NULL,
    auto_title BOOLEAN DEFAULT TRUE,  -- 是否自动生成对话标题
    message_limit INTEGER DEFAULT 100,  -- 单次对话消息数限制
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

## 索引设计

```sql
-- 用户表索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);

-- 会话表索引
CREATE INDEX idx_conversations_user_id ON conversations(user_id);
CREATE INDEX idx_conversations_updated_at ON conversations(updated_at DESC);

-- 消息表索引
CREATE INDEX idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX idx_messages_created_at ON messages(created_at);
CREATE INDEX idx_messages_content ON messages(content);  -- 用于搜索功能

-- 用户设置表索引
CREATE INDEX idx_user_settings_user_id ON user_settings(user_id);
```

## 表结构说明

### 用户表 (users)
- **id**: 主键，自增
- **username**: 用户名，唯一
- **email**: 邮箱，唯一，用于登录
- **password_hash**: 密码哈希值
- **nickname**: 昵称，用于显示
- **avatar**: 头像文件路径
- **theme_preference**: 主题偏好设置
- **created_at/updated_at**: 创建和更新时间

### 会话表 (conversations)
- **id**: 主键，自增
- **user_id**: 用户ID，外键
- **title**: 会话标题，默认为"新对话"
- **created_at/updated_at**: 创建和更新时间

### 消息表 (messages)
- **id**: 主键，自增
- **conversation_id**: 会话ID，外键
- **role**: 消息角色，区分用户和AI
- **content**: 消息内容，支持Markdown
- **created_at**: 创建时间

### 用户设置表 (user_settings)
- **id**: 主键，自增
- **user_id**: 用户ID，外键，唯一
- **auto_title**: 是否自动生成对话标题
- **message_limit**: 单次对话消息数限制
- **created_at/updated_at**: 创建和更新时间

## 设计特点

1. **简洁性**: 只包含必要字段，避免冗余
2. **扩展性**: 预留了用户设置表，便于后续功能扩展
3. **性能**: 合理的索引设计，支持快速查询和搜索
4. **数据完整性**: 使用外键约束确保数据一致性
5. **用户体验**: 支持主题切换、对话标题等个性化功能

## 查询示例

### 获取用户的所有会话（按时间倒序）
```sql
SELECT * FROM conversations 
WHERE user_id = ? 
ORDER BY updated_at DESC;
```

### 获取会话的所有消息
```sql
SELECT * FROM messages 
WHERE conversation_id = ? 
ORDER BY created_at ASC;
```

### 搜索消息内容
```sql
SELECT m.*, c.title 
FROM messages m 
JOIN conversations c ON m.conversation_id = c.id 
WHERE c.user_id = ? AND m.content LIKE '%关键词%' 
ORDER BY m.created_at DESC;
```

### 按日期分组获取会话
```sql
SELECT DATE(created_at) as date, COUNT(*) as count 
FROM conversations 
WHERE user_id = ? 
GROUP BY DATE(created_at) 
ORDER BY date DESC;
```
