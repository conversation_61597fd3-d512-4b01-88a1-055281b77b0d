# 智语慧聊—聊天助手智能体系统

## 系统功能

(1) 用户/管理员系统

1）注册/登录功能及其页面（比如账号+密码或者邮箱+密码+验证码形式）
2）用户个人资料管理（比如头像、昵称和个人信息与偏好的修改）

(2) 核心对话功能

1）文字对话：对接1个AI大模型的API

2）实时对话显示：区分用户与AI的消息样式，显示聊天框图。

(3) 系统个性化设置：要求现代化且美观的前端设计风格，支持明亮/暗色等多主题切换。

(4) 历史记录管理

1）.按日期分组的对话历史列表

2）.对话内容关键词搜索功能。

要求支持上下文

## 技术栈

后端：Django

前端：Bootstrap

数据库：SQLite3

## 实现要求

- 后端需要合理分层，根据一般python项目规范完成分层，确保不同文件的职责不同
- 前端需要使用BootCDN进行节点加速，确保响应及时

## 界面设计要求
- 设计风格：简洁现代，优先使用 Bootstrap 原生组件与变量；支持亮/暗主题自动与手动切换。
- 布局结构：顶部导航（Logo/新建会话/主题切换/用户菜单），左侧会话列表（可折叠），右侧聊天区；无需适配移动端。
- 聊天区：气泡式消息（用户/AI配色区分、头像、时间、复制按钮），支持 Markdown 渲染、代码高亮、流式输出。
- 输入区：多行输入，Enter 发送/Shift+Enter 换行，发送按钮与清空按钮，文件上传入口预留。
- 历史与搜索：按日期分组显示，关键词搜索与高亮，固定顶部搜索框。
- 反馈状态：加载骨架/打字动画、空态占位、错误 Toast 与重试入口。
- 可用性与无障碍：全端响应式（≥360px），统一字号/间距，键盘可达（Tab 顺序），ARIA 标签与可读对比度。
- 性能与资源：静态资源使用 BootCDN，图片优先 SVG，长列表懒加载或虚拟滚动。
- 一致性：统一色板、8px 圆角、适度阴影与卡片化容器，状态色与交互反馈保持一致。

## API调用信息

模型名称：qwen3-235b-a22b-instruct-2507

API Key：sk-e2a07e07cdcc4bb3bf645feda1f1c265

## 邮箱配置

**POP3/IMAP/SMTP/Exchange/CardDAV 授权码**：bhvndaepxbrkcaij